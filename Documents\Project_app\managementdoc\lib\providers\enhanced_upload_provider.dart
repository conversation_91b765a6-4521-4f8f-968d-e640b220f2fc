import 'package:flutter/foundation.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../services/upload_queue_service.dart';
import '../services/cloud_functions_service.dart';
import '../services/enhanced_file_upload_service.dart';

/// Export types for external use
export '../services/upload_queue_service.dart' show UploadProgress;
export '../services/enhanced_file_upload_service.dart'
    show UploadPreflightResult;

/// Enhanced upload provider with Cloud Functions integration
class EnhancedUploadProvider extends ChangeNotifier {
  final UploadQueueService _queueService = UploadQueueService.instance;
  final CloudFunctionsService _cloudFunctions = CloudFunctionsService.instance;
  final EnhancedFileUploadService _uploadService =
      EnhancedFileUploadService.instance;

  String? _currentCategoryId;
  Map<String, dynamic>? _storageStats;
  bool _isStorageNearLimit = false;
  bool _isStorageExceeded = false;

  // Getters
  List<UploadFileModel> get queuedFiles => _queueService.queuedFiles;
  List<UploadFileModel> get activeFiles => _queueService.activeFiles;
  int get queueLength => _queueService.queueLength;
  int get activeUploadsCount => _queueService.activeUploadsCount;
  int get totalUploadsCount => _queueService.totalUploadsCount;
  bool get isProcessing => _queueService.isProcessing;
  String? get currentCategoryId => _currentCategoryId;
  Map<String, dynamic>? get storageStats => _storageStats;
  bool get isStorageNearLimit => _isStorageNearLimit;
  bool get isStorageExceeded => _isStorageExceeded;

  EnhancedUploadProvider() {
    // Listen to queue service changes
    _queueService.addListener(_onQueueServiceChanged);

    // Initialize storage stats
    _updateStorageStats();
  }

  /// Set current category for uploads
  void setCurrentCategory(String? categoryId) {
    _currentCategoryId = categoryId;
    notifyListeners();
  }

  /// Add files to upload queue
  Future<void> addFiles(
    List<XFile> files, {
    String? categoryId,
    Function()? onComplete,
  }) async {
    try {
      debugPrint('📁 Adding ${files.length} files to enhanced upload queue');

      // Check storage quota before adding files
      await _updateStorageStats();

      if (_isStorageExceeded) {
        throw Exception('Storage quota exceeded. Cannot upload more files.');
      }

      if (_isStorageNearLimit) {
        debugPrint('⚠️ Warning: Storage is near limit');
      }

      // Use provided category or current category
      final effectiveCategoryId = categoryId ?? _currentCategoryId;

      await _queueService.addFiles(
        files,
        categoryId: effectiveCategoryId,
        onComplete: () {
          _updateStorageStats();
          onComplete?.call();
        },
      );

      notifyListeners();
    } catch (e) {
      debugPrint('❌ Failed to add files: $e');
      rethrow;
    }
  }

  /// Add single file to queue
  Future<void> addFile(XFile file, {String? categoryId}) async {
    await addFiles([file], categoryId: categoryId);
  }

  /// Get progress stream for a specific file
  Stream<UploadProgress>? getProgressStream(String fileId) {
    return _queueService.getProgressStream(fileId);
  }

  /// Retry failed upload
  void retryUpload(String fileId) {
    _queueService.retryUpload(fileId);
    notifyListeners();
  }

  /// Pause upload
  void pauseUpload(String fileId) {
    _queueService.pauseUpload(fileId);
    notifyListeners();
  }

  /// Resume upload
  void resumeUpload(String fileId) {
    _queueService.resumeUpload(fileId);
    notifyListeners();
  }

  /// Cancel upload
  void cancelUpload(String fileId) {
    _queueService.cancelUpload(fileId);
    notifyListeners();
  }

  /// Remove completed upload from display
  void removeCompletedUpload(String fileId) {
    _queueService.removeCompletedUpload(fileId);
    notifyListeners();
  }

  /// Clear all completed uploads
  void clearCompleted() {
    _queueService.clearCompleted();
    notifyListeners();
  }

  /// Pause all uploads
  void pauseAll() {
    _queueService.pauseAll();
    notifyListeners();
  }

  /// Resume all paused uploads
  void resumeAll() {
    _queueService.resumeAll();
    notifyListeners();
  }

  /// Cancel all uploads
  void cancelAll() {
    _queueService.cancelAll();
    notifyListeners();
  }

  /// Set maximum concurrent uploads
  void setMaxConcurrentUploads(int max) {
    _queueService.setMaxConcurrentUploads(max);
    notifyListeners();
  }

  /// Get upload statistics
  Map<String, dynamic> getUploadStats() {
    return _queueService.getUploadStats();
  }

  /// Update storage statistics
  Future<void> _updateStorageStats() async {
    try {
      _storageStats = await _cloudFunctions.getStorageStats();
      _isStorageNearLimit = _storageStats?['isNearLimit'] ?? false;
      _isStorageExceeded = _storageStats?['isExceeded'] ?? false;

      debugPrint('📊 Storage stats updated: $_storageStats');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Failed to update storage stats: $e');
      // Don't throw error, just log it
    }
  }

  /// Refresh storage statistics manually
  Future<void> refreshStorageStats() async {
    await _updateStorageStats();
  }

  /// Get storage usage percentage
  double getStorageUsagePercentage() {
    if (_storageStats == null) return 0.0;
    return (_storageStats!['usagePercentage'] as num?)?.toDouble() ?? 0.0;
  }

  /// Get formatted storage usage string
  String getStorageUsageString() {
    if (_storageStats == null) return 'Unknown';

    final used = _storageStats!['used'] as int? ?? 0;
    final limit = _storageStats!['limit'] as int? ?? 0;

    if (limit == 0) return 'No limit';

    return '${_formatBytes(used)} / ${_formatBytes(limit)}';
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Check if upload is allowed based on storage quota
  bool canUpload() {
    return !_isStorageExceeded;
  }

  /// Get storage warning message
  String? getStorageWarningMessage() {
    if (_isStorageExceeded) {
      return 'Storage quota exceeded. Please free up space before uploading.';
    } else if (_isStorageNearLimit) {
      return 'Storage is nearly full. Consider cleaning up old files.';
    }
    return null;
  }

  /// Cleanup orphaned files
  Future<Map<String, dynamic>> cleanupOrphanedFiles() async {
    try {
      debugPrint('🧹 Starting orphaned files cleanup');
      final result = await _cloudFunctions.cleanupOrphanedFiles();

      // Refresh storage stats after cleanup
      await _updateStorageStats();

      debugPrint(
        '✅ Cleanup completed: ${result['deletedCount']} files removed',
      );
      return result;
    } catch (e) {
      debugPrint('❌ Cleanup failed: $e');
      rethrow;
    }
  }

  /// Perform preflight check before upload
  Future<UploadPreflightResult> performPreflightCheck() async {
    return await _uploadService.preflightCheck();
  }

  /// Get file access URL with expiration
  Future<String> getFileAccessUrl({
    required String filePath,
    Duration? expiration,
  }) async {
    return await _cloudFunctions.getFileAccessUrl(
      filePath: filePath,
      expiration: expiration,
    );
  }

  /// Batch process files
  Future<List<Map<String, dynamic>>> batchProcessFiles({
    required List<String> filePaths,
    required String operation,
    Map<String, dynamic>? options,
  }) async {
    return await _cloudFunctions.batchProcessFiles(
      filePaths: filePaths,
      operation: operation,
      options: options,
    );
  }

  /// Handle queue service changes
  void _onQueueServiceChanged() {
    notifyListeners();
  }

  @override
  void dispose() {
    _queueService.removeListener(_onQueueServiceChanged);
    _queueService.dispose();
    _uploadService.dispose();
    super.dispose();
  }
}
