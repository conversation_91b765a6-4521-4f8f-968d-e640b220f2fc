import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/foundation.dart';

class CloudFunctionsService {
  static CloudFunctionsService? _instance;
  static CloudFunctionsService get instance =>
      _instance ??= CloudFunctionsService._();

  CloudFunctionsService._();

  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  // Category Management Functions

  /// Create a new category using Cloud Functions
  Future<Map<String, dynamic>> createCategory({
    required String name,
    String? description,
    List<String>? permissions,
    bool? isActive,
  }) async {
    try {
      debugPrint('🔄 Creating category via Cloud Functions: $name');

      final HttpsCallable callable = _functions.httpsCallable('createCategory');
      final result = await callable.call({
        'name': name,
        'description': description ?? '',
        'permissions': permissions ?? [],
        'isActive': isActive ?? true,
      });

      debugPrint('✅ Category created successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to create category: $e');
      rethrow;
    }
  }

  /// Update an existing category using Cloud Functions
  Future<Map<String, dynamic>> updateCategory({
    required String categoryId,
    String? name,
    String? description,
    List<String>? permissions,
    bool? isActive,
  }) async {
    try {
      debugPrint('🔄 Updating category via Cloud Functions: $categoryId');

      final HttpsCallable callable = _functions.httpsCallable('updateCategory');
      final result = await callable.call({
        'categoryId': categoryId,
        'name': name,
        'description': description,
        'permissions': permissions,
        'isActive': isActive,
      });

      debugPrint('✅ Category updated successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to update category: $e');
      rethrow;
    }
  }

  /// Delete a category using Cloud Functions
  Future<Map<String, dynamic>> deleteCategory(String categoryId) async {
    try {
      debugPrint('🔄 Deleting category via Cloud Functions: $categoryId');

      final HttpsCallable callable = _functions.httpsCallable('deleteCategory');
      final result = await callable.call({'categoryId': categoryId});

      debugPrint('✅ Category deleted successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to delete category: $e');
      rethrow;
    }
  }

  /// Add files to a category using Cloud Functions
  Future<Map<String, dynamic>> addFilesToCategory({
    required String categoryId,
    required List<String> documentIds,
  }) async {
    try {
      debugPrint(
        '🔄 Adding ${documentIds.length} files to category via Cloud Functions: $categoryId',
      );

      final HttpsCallable callable = _functions.httpsCallable(
        'addFilesToCategory',
      );
      final result = await callable.call({
        'categoryId': categoryId,
        'documentIds': documentIds,
      });

      debugPrint('✅ Files added to category successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to add files to category: $e');
      rethrow;
    }
  }

  /// Remove files from a category using Cloud Functions
  Future<Map<String, dynamic>> removeFilesFromCategory({
    required String categoryId,
    required List<String> documentIds,
  }) async {
    try {
      debugPrint(
        '🔄 Removing ${documentIds.length} files from category via Cloud Functions: $categoryId',
      );

      final HttpsCallable callable = _functions.httpsCallable(
        'removeFilesFromCategory',
      );
      final result = await callable.call({
        'categoryId': categoryId,
        'documentIds': documentIds,
      });

      debugPrint('✅ Files removed from category successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to remove files from category: $e');
      rethrow;
    }
  }

  // File Upload Functions

  /// Process file upload using Cloud Functions
  Future<Map<String, dynamic>> processFileUpload({
    required String filePath,
    required String fileName,
    required String contentType,
    Map<String, String>? metadata,
  }) async {
    try {
      debugPrint('🔄 Processing file upload via Cloud Functions: $fileName');

      final HttpsCallable callable = _functions.httpsCallable(
        'processFileUpload',
      );
      final result = await callable.call({
        'filePath': filePath,
        'fileName': fileName,
        'contentType': contentType,
        'metadata': metadata ?? {},
      });

      debugPrint('✅ File upload processed successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to process file upload: $e');
      rethrow;
    }
  }

  /// Validate file using Cloud Functions
  Future<Map<String, dynamic>> validateFile({
    required String filePath,
    required String contentType,
    required int fileSize,
  }) async {
    try {
      debugPrint('🔄 Validating file via Cloud Functions: $filePath');

      final HttpsCallable callable = _functions.httpsCallable('validateFile');
      final result = await callable.call({
        'filePath': filePath,
        'contentType': contentType,
        'fileSize': fileSize,
      });

      debugPrint('✅ File validated successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to validate file: $e');
      rethrow;
    }
  }

  // Sync Operations Functions

  /// Perform comprehensive sync using Cloud Functions
  Future<Map<String, dynamic>> performComprehensiveSync() async {
    try {
      debugPrint('🔄 Performing comprehensive sync via Cloud Functions');

      final HttpsCallable callable = _functions.httpsCallable(
        'performComprehensiveSync',
      );
      final result = await callable.call({});

      debugPrint('✅ Comprehensive sync completed: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to perform comprehensive sync: $e');
      rethrow;
    }
  }

  /// Sync storage with Firestore using Cloud Functions
  Future<Map<String, dynamic>> syncStorageWithFirestore() async {
    try {
      debugPrint('🔄 Syncing storage with Firestore via Cloud Functions');

      final HttpsCallable callable = _functions.httpsCallable(
        'syncStorageWithFirestore',
      );
      final result = await callable.call({});

      debugPrint('✅ Storage sync completed: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to sync storage: $e');
      rethrow;
    }
  }

  /// Cleanup orphaned metadata using Cloud Functions
  Future<Map<String, dynamic>> cleanupOrphanedMetadata() async {
    try {
      debugPrint('🔄 Cleaning up orphaned metadata via Cloud Functions');

      final HttpsCallable callable = _functions.httpsCallable(
        'cleanupOrphanedMetadata',
      );
      final result = await callable.call({});

      debugPrint('✅ Orphaned metadata cleanup completed: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to cleanup orphaned metadata: $e');
      rethrow;
    }
  }

  // Notification Functions

  /// Send notification using Cloud Functions
  Future<Map<String, dynamic>> sendNotification({
    required String userId,
    required String title,
    required String message,
    String type = 'info',
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🔄 Sending notification via Cloud Functions to: $userId');

      final HttpsCallable callable = _functions.httpsCallable(
        'sendNotification',
      );
      final result = await callable.call({
        'userId': userId,
        'title': title,
        'message': message,
        'type': type,
        'data': data ?? {},
      });

      debugPrint('✅ Notification sent successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to send notification: $e');
      rethrow;
    }
  }

  // User Management Functions

  /// Create user using Cloud Functions
  Future<Map<String, dynamic>> createUser({
    required String email,
    required String password,
    required String fullName,
    required String userType,
    List<String>? permissions,
  }) async {
    try {
      debugPrint('🔄 Creating user via Cloud Functions: $email');

      final HttpsCallable callable = _functions.httpsCallable('createUser');
      final result = await callable.call({
        'email': email,
        'password': password,
        'fullName': fullName,
        'userType': userType,
        'permissions': permissions ?? [],
      });

      debugPrint('✅ User created successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to create user: $e');
      rethrow;
    }
  }

  /// Update user permissions using Cloud Functions
  Future<Map<String, dynamic>> updateUserPermissions({
    required String userId,
    required List<String> permissions,
    String? userType,
  }) async {
    try {
      debugPrint('🔄 Updating user permissions via Cloud Functions: $userId');

      final HttpsCallable callable = _functions.httpsCallable(
        'updateUserPermissions',
      );
      final result = await callable.call({
        'userId': userId,
        'permissions': permissions,
        'userType': userType,
      });

      debugPrint('✅ User permissions updated successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to update user permissions: $e');
      rethrow;
    }
  }

  /// Delete user using Cloud Functions
  Future<Map<String, dynamic>> deleteUser(String userId) async {
    try {
      debugPrint('🔄 Deleting user via Cloud Functions: $userId');

      final HttpsCallable callable = _functions.httpsCallable('deleteUser');
      final result = await callable.call({'userId': userId});

      debugPrint('✅ User deleted successfully: ${result.data}');
      return Map<String, dynamic>.from(result.data);
    } catch (e) {
      debugPrint('❌ Failed to delete user: $e');
      rethrow;
    }
  }
}
